const fetch = require('node-fetch');

class LlamaIndexService {
  constructor() {
    // Initialize with fallback values until config is loaded
    this.apiKey = process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
    this.isConfigured = !!this.apiKey;

    // Initialize configuration
    this.initializeConfig();
  }

  /**
   * Initialize configuration asynchronously
   */
  async initializeConfig() {
    try {
      const configManager = require('../config');
      
      // Try to get config, but don't fail if it's not ready yet
      if (configManager.isInitialized) {
        const config = configManager.getConfig();
        this.updateConfiguration(config);
      } else {
        // Config will be updated later when it's ready
        console.log('📄 LlamaIndexService: Using fallback values until configuration is loaded');
        this.checkConfiguration();
      }
    } catch (error) {
      console.log('📄 LlamaIndexService: Using environment variables for configuration');
      this.checkConfiguration();
    }
  }

  /**
   * Update configuration when it becomes available
   */
  updateConfiguration(config) {
    if (config && config.llamaIndex) {
      this.apiKey = config.llamaIndex.apiKey || process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
      this.baseUrl = config.llamaIndex.baseUrl || 'https://api.cloud.llamaindex.ai/api/v1';
      this.isConfigured = !!this.apiKey;
      
      console.log('📄 LlamaIndexService Configuration Updated:');
      console.log(`   Base URL: ${this.baseUrl}`);
      console.log(`   API Key: ${this.apiKey ? '✅ Available' : '❌ REQUIRED'}`);
      
      if (!this.isConfigured) {
        console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Vector retrieval will be disabled.');
      } else {
        console.log('✅ LlamaIndexService configured successfully');
      }
    }
  }

  /**
   * Check initial configuration
   */
  checkConfiguration() {
    console.log('📄 LlamaIndexService Configuration:');
    console.log(`   Base URL: ${this.baseUrl}`);
    console.log(`   API Key: ${this.apiKey ? '✅ Available' : '❌ REQUIRED'}`);
    
    if (!this.isConfigured) {
      console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Vector retrieval will be disabled.');
    } else {
      console.log('✅ LlamaIndexService initialized successfully');
    }
  }

  /**
   * Check if service is configured
   */
  checkConfigurationForOperation() {
    if (!this.isConfigured) {
      throw new Error('LlamaIndex service is not configured. Please set LLAMA_CLOUD_API_KEY.');
    }
  }

  /**
   * Retrieve relevant chunks from vector index using retriever
   * @param {string} retrieverId - LlamaIndex retriever ID
   * @param {string} query - Search query
   * @param {number} topK - Number of top results to return
   * @param {number} similarityThreshold - Minimum similarity score (0-1)
   * @returns {Promise<Object>} Retrieval result with nodes
   */
  async retrieve(retrieverId, query, topK = 10, similarityThreshold = 0.1) {
    this.checkConfigurationForOperation();

    try {
      console.log(`🔍 Retrieving from retriever ${retrieverId} for query: "${query.substring(0, 50)}..."`);
      console.log(`📊 Parameters: topK=${topK}, similarityThreshold=${similarityThreshold}`);

      const payload = {
        query,
        top_k: topK,
        // Add similarity threshold if supported
        ...(similarityThreshold && { similarity_threshold: similarityThreshold })
      };

      console.log(`📤 Sending payload:`, JSON.stringify(payload, null, 2));

      const response = await fetch(`${this.baseUrl}/retrievers/${retrieverId}/retrieve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ LlamaIndex retrieve error: ${response.status} - ${errorText}`);
        throw new Error(`Retrieval failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log(`📥 Raw response:`, JSON.stringify(result, null, 2));
      console.log(`✅ Retrieved ${result.nodes?.length || 0} relevant chunks`);

      // Log details about each node for debugging
      if (result.nodes && result.nodes.length > 0) {
        result.nodes.forEach((node, index) => {
          console.log(`📄 Node ${index + 1}:`, {
            score: node.score,
            textLength: node.text?.length || 0,
            textPreview: node.text?.substring(0, 100) + '...'
          });
        });
      } else {
        console.warn(`⚠️ No nodes returned for query: "${query}"`);
      }

      return result;
    } catch (error) {
      console.error(`❌ LlamaIndex retrieve error:`, error.message);
      throw error;
    }
  }

  /**
   * Retrieve context from multiple documents in parallel
   * @param {Array} documents - Array of document objects with indexId
   * @param {string} query - Search query
   * @param {number} topK - Number of top results per document
   * @returns {Promise<string>} Combined context from all documents
   */
  async retrieveFromMultipleDocuments(documents, query, topK = 10) {
    this.checkConfigurationForOperation();

    if (!documents || documents.length === 0) {
      return '';
    }

    console.log(`🔍 Retrieving from ${documents.length} documents in parallel`);

    // Create parallel retrieval promises with improved parameters
    const retrievalPromises = documents.map(async (doc) => {
      try {
        // Try with lower similarity threshold first
        const result = await this.retrieve(doc.indexId, query, topK, 0.1);

        // Check if we got any results
        if (result.nodes && result.nodes.length > 0) {
          return {
            filename: doc.filename,
            documentId: doc.id,
            nodes: result.nodes,
            success: true,
            method: 'vector_search'
          };
        } else {
          // No results from vector search, try fallback
          console.log(`🔄 No vector results for ${doc.filename}, trying fallback with lower threshold`);
          const fallbackResult = await this.retrieve(doc.indexId, query, topK * 2, 0.01);

          if (fallbackResult.nodes && fallbackResult.nodes.length > 0) {
            return {
              filename: doc.filename,
              documentId: doc.id,
              nodes: fallbackResult.nodes,
              success: true,
              method: 'fallback_vector_search'
            };
          } else {
            // Still no results, use full document text
            console.log(`📄 No vector results found, using full document text as fallback for ${doc.filename}`);
            if (doc.parsedData && doc.parsedData.text) {
              return {
                filename: doc.filename,
                documentId: doc.id,
                nodes: [{
                  text: doc.parsedData.text,
                  score: 0.5, // Default score for full text
                  metadata: { source: 'full_document_fallback' }
                }],
                success: true,
                method: 'full_text_fallback'
              };
            } else {
              console.warn(`⚠️ No parsed text available for ${doc.filename}`);
              return {
                filename: doc.filename,
                documentId: doc.id,
                nodes: [],
                success: false,
                error: 'No vector results and no parsed text available',
                method: 'failed'
              };
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ Vector search failed for ${doc.filename}:`, error.message);

        // Final fallback: Use document's parsed text if available
        if (doc.parsedData && doc.parsedData.text) {
          console.log(`📄 Using full document text as error fallback for ${doc.filename}`);
          return {
            filename: doc.filename,
            documentId: doc.id,
            nodes: [{
              text: doc.parsedData.text,
              score: 0.5, // Default score for full text
              metadata: { source: 'error_fallback' }
            }],
            success: true,
            method: 'error_fallback'
          };
        }

        return {
          filename: doc.filename,
          documentId: doc.id,
          nodes: [],
          success: false,
          error: error.message,
          method: 'failed'
        };
      }
    });

    // Wait for all retrievals to complete
    const results = await Promise.all(retrievalPromises);

    // Combine context from successful retrievals
    let context = '';
    let successfulRetrievals = 0;
    const methodCounts = {};

    results.forEach(({ filename, nodes, success, method }) => {
      if (success && nodes.length > 0) {
        // context += `--- ${filename} ---\n`;
        context += nodes.map(node => node.text).join('\n\n');
        context += '\n\n';
        successfulRetrievals++;
        methodCounts[method] = (methodCounts[method] || 0) + 1;
      }
    });

    console.log(`✅ Successfully retrieved context from ${successfulRetrievals}/${documents.length} documents`);
    console.log(`📊 Retrieval methods used:`, methodCounts);

    return context;
  }
}

module.exports = new LlamaIndexService();
