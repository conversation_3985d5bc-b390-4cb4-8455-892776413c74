#!/usr/bin/env node

/**
 * Performance Test Script for ChatAI Streaming API
 * Tests the optimizations made to reduce streaming response delay
 */

const https = require('https');
const http = require('http');

const API_BASE = 'http://localhost:3001/api/v1/chat/';
const API_KEY = 'test_api_key_1752470355743_kshejjgq3';

// Test queries of different complexity
const testQueries = [
  { query: 'hi', type: 'simple', expected: 'fast' },
  { query: 'hello', type: 'simple', expected: 'fast' },
  { query: 'what is the compensation policy', type: 'complex', expected: 'medium' },
  { query: 'how to implement database configuration', type: 'complex', expected: 'medium' }
];

async function testStreamingPerformance(query, testMode = true) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let firstDataTime = null;
    let endTime = null;
    let sessionId = null;
    let contentReceived = '';
    let statusUpdates = [];

    const url = `${API_BASE}?apikey=${encodeURIComponent(API_KEY)}&query=${encodeURIComponent(query)}&stream=true&testMode=${testMode}`;
    
    console.log(`\n🧪 Testing: "${query}" (testMode: ${testMode})`);
    console.log(`📡 URL: ${url}`);

    const req = http.get(url, (res) => {
      console.log(`📊 Response status: ${res.statusCode}`);
      
      if (res.statusCode !== 200) {
        reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
        return;
      }

      res.on('data', (chunk) => {
        if (!firstDataTime) {
          firstDataTime = Date.now();
          console.log(`⚡ First data received: ${firstDataTime - startTime}ms`);
        }

        const data = chunk.toString();
        const lines = data.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonData = JSON.parse(line.slice(6));
              
              if (jsonData.type === 'session') {
                sessionId = jsonData.sessionId;
                console.log(`🔗 Session established: ${sessionId.substring(0, 8)}...`);
              } else if (jsonData.type === 'status') {
                statusUpdates.push({
                  message: jsonData.message,
                  time: Date.now() - startTime
                });
                console.log(`📢 Status: ${jsonData.message} (${Date.now() - startTime}ms)`);
              } else if (jsonData.type === 'content') {
                contentReceived += jsonData.content;
              } else if (jsonData.type === 'done') {
                endTime = Date.now();
                const totalTime = jsonData.timing?.total || (endTime - startTime);
                console.log(`✅ Stream completed: ${totalTime}ms`);
                
                resolve({
                  query,
                  testMode,
                  startTime,
                  firstDataTime,
                  endTime,
                  totalTime,
                  timeToFirstData: firstDataTime - startTime,
                  sessionId,
                  contentLength: contentReceived.length,
                  statusUpdates,
                  success: true
                });
                return;
              }
            } catch (parseError) {
              console.warn(`⚠️ Failed to parse SSE data: ${line}`);
            }
          }
        }
      });

      res.on('end', () => {
        if (!endTime) {
          endTime = Date.now();
          resolve({
            query,
            testMode,
            startTime,
            firstDataTime,
            endTime,
            totalTime: endTime - startTime,
            timeToFirstData: firstDataTime ? firstDataTime - startTime : null,
            sessionId,
            contentLength: contentReceived.length,
            statusUpdates,
            success: false,
            error: 'Stream ended without done signal'
          });
        }
      });

      res.on('error', (error) => {
        reject(error);
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    // Set timeout
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function runPerformanceTests() {
  console.log('🚀 Starting ChatAI Streaming Performance Tests');
  console.log('=' .repeat(60));

  const results = [];

  for (const testCase of testQueries) {
    try {
      // Test with testMode=true (fast path)
      const testModeResult = await testStreamingPerformance(testCase.query, true);
      results.push(testModeResult);

      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test with testMode=false (full processing) - only if we have valid API key
      // For now, skip this as we don't have valid API key
      console.log(`⏭️ Skipping full processing test (no valid API key)`);

    } catch (error) {
      console.error(`❌ Test failed for "${testCase.query}": ${error.message}`);
      results.push({
        query: testCase.query,
        error: error.message,
        success: false
      });
    }
  }

  // Print summary
  console.log('\n📊 PERFORMANCE TEST RESULTS');
  console.log('=' .repeat(60));

  results.forEach((result, index) => {
    if (result.success) {
      console.log(`\n${index + 1}. Query: "${result.query}"`);
      console.log(`   ⚡ Time to first data: ${result.timeToFirstData}ms`);
      console.log(`   🏁 Total time: ${result.totalTime}ms`);
      console.log(`   📝 Content length: ${result.contentLength} chars`);
      console.log(`   📢 Status updates: ${result.statusUpdates.length}`);
      
      if (result.statusUpdates.length > 0) {
        result.statusUpdates.forEach(status => {
          console.log(`      - ${status.message} (${status.time}ms)`);
        });
      }
    } else {
      console.log(`\n${index + 1}. Query: "${result.query}" - FAILED`);
      console.log(`   ❌ Error: ${result.error}`);
    }
  });

  // Calculate averages for successful tests
  const successfulTests = results.filter(r => r.success);
  if (successfulTests.length > 0) {
    const avgTimeToFirstData = successfulTests.reduce((sum, r) => sum + r.timeToFirstData, 0) / successfulTests.length;
    const avgTotalTime = successfulTests.reduce((sum, r) => sum + r.totalTime, 0) / successfulTests.length;

    console.log('\n📈 AVERAGES');
    console.log(`   ⚡ Average time to first data: ${avgTimeToFirstData.toFixed(1)}ms`);
    console.log(`   🏁 Average total time: ${avgTotalTime.toFixed(1)}ms`);
  }

  console.log('\n✅ Performance tests completed!');
}

// Run the tests
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

module.exports = { testStreamingPerformance, runPerformanceTests };
